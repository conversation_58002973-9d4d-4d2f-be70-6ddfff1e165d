<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSM Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body class="bg-light">
    <div class="container-fluid p-3">
        <!-- Header Section -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>FSM Dashboard</h4>
                    </div>
                    <div class="card-body">
                        <!-- Previous Year Trend Section -->
                        <div class="row align-items-center mb-3">
                            <div class="col-md-2">
                                <label class="form-label fw-bold">From Year:</label>
                                <select class="form-select form-select-sm" id="fromYear">
                                    <option>Select</option>
                                    <option>2023</option>
                                    <option>2024</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold">To Year:</label>
                                <select class="form-select form-select-sm" id="toYear">
                                    <option>Select</option>
                                    <option>2023</option>
                                    <option>2024</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-success btn-sm mt-4">
                                    <i class="fas fa-chart-line me-1"></i>Generate
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="behaviorType" id="professional" checked>
                                            <label class="form-check-label" for="professional">Professional Behaviour</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="behaviorType" id="employee">
                                            <label class="form-check-label" for="employee">Employee SMS</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="behaviorType" id="parts">
                                            <label class="form-check-label" for="parts">Parts Support</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="behaviorType" id="overall">
                                            <label class="form-check-label" for="overall">Over All Rating</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="row g-3">
            <!-- Left Column - Data Table and Charts -->
            <div class="col-lg-8">
                <!-- Data Table -->
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-table me-2"></i>Zone - Machine Summary</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="table-responsive" style="max-height: 200px;">
                            <table class="table table-sm table-striped" id="summaryTable">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>Branch</th>
                                        <th>Zone</th>
                                        <th>State</th>
                                        <th>Service Engineer</th>
                                        <th>Transaction Date</th>
                                        <th>Transaction Party Name</th>
                                        <th>Brand</th>
                                        <th>Model</th>
                                        <th>Serial #</th>
                                        <th>Job Address</th>
                                        <th>Transaction Status</th>
                                        <th>Ageing $</th>
                                        <th>SRO</th>
                                        <th>Quotation/JOB</th>
                                        <th>SR/JC #</th>
                                        <th>JC Status</th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row g-2">
                    <!-- Feedback Rating Chart -->
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-info text-white py-2">
                                <h6 class="mb-0"><i class="fas fa-star me-2"></i>Feedback Rating</h6>
                            </div>
                            <div class="card-body p-2">
                                <canvas id="feedbackChart" height="150"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Response Chart -->
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-warning text-dark py-2">
                                <h6 class="mb-0"><i class="fas fa-comments me-2"></i>Feedback Response</h6>
                            </div>
                            <div class="card-body p-2">
                                <canvas id="responseChart" height="150"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Selection Criteria and Analytics -->
            <div class="col-lg-4">
                <!-- Selection Criteria -->
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Selection Criteria</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="row g-2">
                            <div class="col-4">
                                <label class="form-label fw-bold small">Zone</label>
                                <div class="border rounded p-2" style="height: 120px; overflow-y: auto;">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="zone1" checked>
                                        <label class="form-check-label small" for="zone1">Zone 1</label>
                                    </div>
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="zone2" checked>
                                        <label class="form-check-label small" for="zone2">Zone 2</label>
                                    </div>
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="zone3" checked>
                                        <label class="form-check-label small" for="zone3">Zone 3-6</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <label class="form-label fw-bold small">Equipment</label>
                                <div class="border rounded p-2" style="height: 120px; overflow-y: auto;">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="eq1" checked>
                                        <label class="form-check-label small" for="eq1">Equipment 1</label>
                                    </div>
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="eq2" checked>
                                        <label class="form-check-label small" for="eq2">Equipment 2</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <label class="form-label fw-bold small">Branch</label>
                                <div class="border rounded p-2" style="height: 120px; overflow-y: auto;">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="pune" checked>
                                        <label class="form-check-label small" for="pune">Pune</label>
                                    </div>
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="mumbai" checked>
                                        <label class="form-check-label small" for="mumbai">Mumbai</label>
                                    </div>
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" id="nagpur" checked>
                                        <label class="form-check-label small" for="nagpur">Nagpur</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <label class="form-label fw-bold small">From Date</label>
                                <input type="date" class="form-control form-control-sm" value="2025-08-01">
                            </div>
                            <div class="col-6">
                                <label class="form-label fw-bold small">To Date</label>
                                <input type="date" class="form-control form-control-sm" value="2025-08-28">
                            </div>
                        </div>
                        <button class="btn btn-primary btn-sm mt-2 w-100">
                            <i class="fas fa-sync me-1"></i>Generate
                        </button>
                    </div>
                </div>

                <!-- Zone Map -->
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-primary text-white py-2">
                        <h6 class="mb-0"><i class="fas fa-map me-2"></i>Zone Map</h6>
                    </div>
                    <div class="card-body p-2">
                        <div id="zoneMap" class="text-center">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y4ZjlmYSIgc3Ryb2tlPSIjZGVlMmU2IiBzdHJva2Utd2lkdGg9IjEiLz4KICA8dGV4dCB4PSIxNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNmM3NTdkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5JbnRlcmFjdGl2ZSBab25lIE1hcDwvdGV4dD4KPC9zdmc+" alt="Zone Map" class="img-fluid">
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Zone - Service Request Summary</small>
                            <div class="row text-center mt-1">
                                <div class="col-4"><span class="badge bg-primary">Started: 10</span></div>
                                <div class="col-4"><span class="badge bg-warning">Pending: 5</span></div>
                                <div class="col-4"><span class="badge bg-success">Complete: 15</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tabs -->
                <div class="card shadow-sm">
                    <div class="card-header p-0">
                        <ul class="nav nav-tabs nav-fill" id="analyticsTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active small" id="sr-tab" data-bs-toggle="tab" data-bs-target="#sr" type="button">SR</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="quotation-tab" data-bs-toggle="tab" data-bs-target="#quotation" type="button">Quotation</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="jobcard-tab" data-bs-toggle="tab" data-bs-target="#jobcard" type="button">Job Card</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button">JC Activity</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="machine-tab" data-bs-toggle="tab" data-bs-target="#machine" type="button">Machine Status</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="escalation-tab" data-bs-toggle="tab" data-bs-target="#escalation" type="button">Escalation</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link small" id="workload-tab" data-bs-toggle="tab" data-bs-target="#workload" type="button">Employee Workload</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body p-2">
                        <div class="tab-content" id="analyticsTabContent">
                            <div class="tab-pane fade show active" id="sr" role="tabpanel">
                                <canvas id="srChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="quotation" role="tabpanel">
                                <canvas id="quotationChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="jobcard" role="tabpanel">
                                <canvas id="jobcardChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="activity" role="tabpanel">
                                <canvas id="activityChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="machine" role="tabpanel">
                                <canvas id="machineChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="escalation" role="tabpanel">
                                <canvas id="escalationChart" height="200"></canvas>
                            </div>
                            <div class="tab-pane fade" id="workload" role="tabpanel">
                                <canvas id="workloadChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="Dashboard.js"></script>
</body>
</html>