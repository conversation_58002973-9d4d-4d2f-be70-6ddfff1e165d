{"dashboard": {"title": "FSM Dashboard", "lastUpdated": "2025-08-28T10:30:00Z"}, "behaviorTypes": {"professional": {"name": "Professional Behaviour", "rating": 4.2, "feedbackPercentage": 85, "data": [{"branch": "Mumbai", "zone": "Zone 1", "state": "Maharashtra", "serviceEngineer": "<PERSON><PERSON>", "transactionDate": "2025-08-15", "partyName": "Tata Industries", "brand": "Siemens", "model": "MC1562", "serial": "SN12345", "jobAddress": "Andheri, Mumbai", "transactionStatus": "Completed", "statusColor": "success", "ageing": "3 days", "sro": "SRO001", "quotation": "Q2025001", "srjc": "SR001", "jcStatus": "Closed", "jcStatusColor": "success", "category": "professional"}, {"branch": "Pune", "zone": "Zone 2", "state": "Maharashtra", "serviceEngineer": "<PERSON><PERSON>", "transactionDate": "2025-08-20", "partyName": "Bajaj Auto", "brand": "ABB", "model": "Super 1800", "serial": "SN67890", "jobAddress": "Pimpri, Pune", "transactionStatus": "In Progress", "statusColor": "warning", "ageing": "1 day", "sro": "SRO002", "quotation": "Q2025002", "srjc": "SR002", "jcStatus": "Open", "jcStatusColor": "primary", "category": "professional"}]}, "employee": {"name": "Employee SMS", "rating": 3.8, "feedbackPercentage": 78, "data": [{"branch": "Nagpur", "zone": "Zone 3", "state": "Maharashtra", "serviceEngineer": "<PERSON><PERSON>", "transactionDate": "2025-08-18", "partyName": "Mahindra Group", "brand": "<PERSON>", "model": "HG 2420", "serial": "SN11111", "jobAddress": "MIDC, Nagpur", "transactionStatus": "Pending", "statusColor": "danger", "ageing": "5 days", "sro": "SRO003", "quotation": "Q2025003", "srjc": "SR003", "jcStatus": "Pending", "jcStatusColor": "warning", "category": "employee"}]}, "parts": {"name": "Parts Support", "rating": 4.0, "feedbackPercentage": 82, "data": [{"branch": "Mumbai", "zone": "Zone 1", "state": "Maharashtra", "serviceEngineer": "<PERSON><PERSON>", "transactionDate": "2025-08-22", "partyName": "Reliance Industries", "brand": "Mitsubishi", "model": "SW 16 MC", "serial": "SN22222", "jobAddress": "Navi Mumbai", "transactionStatus": "Parts Ordered", "statusColor": "info", "ageing": "2 days", "sro": "SRO004", "quotation": "Q2025004", "srjc": "SR004", "jcStatus": "Parts Pending", "jcStatusColor": "info", "category": "parts"}]}, "overall": {"name": "Over All Rating", "rating": 4.0, "feedbackPercentage": 81, "data": []}}, "analytics": {"srData": {"labels": ["MC1562", "Non-Roller", "SP-84", "Super 1800", "HG 2420", "SW 16 MC", "HG RW WV", "Others"], "values": [13, 23, 31, 44, 56, 75, 18, 144], "colors": ["#007bff", "#28a745", "#ffc107", "#dc3545", "#6f42c1", "#fd7e14", "#20c997", "#6c757d"]}, "serviceRequestData": {"labels": ["Daily Maintenance", "Good Will", "Customer <PERSON><PERSON><PERSON><PERSON>", "Normal/Payable", "HARC", "FOC"], "values": [48, 25, 88, 5, 2, 2], "percentages": [27.71, 14.5, 51.01, 2.9, 1.16, 1.16]}, "resolutionTime": {"labels": ["More Than 90 Hours", "72 To 90 Hours", "48 To 72 Hours", "24 To 48 Hours", "24 To 36 Hours", "16 To 24 Hours", "8 To 16 Hours", "Less Than 8 Hours"], "values": [13, 5, 5, 0, 0, 0, 0, 30], "percentages": [24.53, 9.43, 9.43, 0, 0, 0, 0, 56.6]}}, "feedback": {"overall": 3.8, "zoneWise": 4.2, "parameterWise": 3.5, "response": {"feedbackNotRequired": 30, "wrongContactPerson": 15, "noFeedback": 25, "feedbackReceived": 30}}, "zones": [{"id": "zone1", "name": "Zone 1", "states": ["Maharashtra", "Gujarat"], "branches": ["Mumbai", "Pune", "Ahmedabad"], "activeJobs": 15, "completedJobs": 45, "pendingJobs": 8}, {"id": "zone2", "name": "Zone 2", "states": ["Karnataka", "Andhra Pradesh"], "branches": ["Bangalore", "Hyderabad"], "activeJobs": 12, "completedJobs": 38, "pendingJobs": 5}, {"id": "zone3", "name": "Zone 3-6", "states": ["Tamil Nadu", "Kerala", "West Bengal"], "branches": ["Chennai", "<PERSON><PERSON>", "Kolkata"], "activeJobs": 18, "completedJobs": 52, "pendingJobs": 10}], "equipment": [{"id": "eq1", "name": "Equipment 1", "type": "Industrial Machine", "count": 125}, {"id": "eq2", "name": "Equipment 2", "type": "Service Tool", "count": 87}], "branches": [{"id": "pune", "name": "Pune", "zone": "Zone 1", "state": "Maharashtra", "activeEngineers": 8, "totalJobs": 156}, {"id": "mumbai", "name": "Mumbai", "zone": "Zone 1", "state": "Maharashtra", "activeEngineers": 12, "totalJobs": 234}, {"id": "nagpur", "name": "Nagpur", "zone": "Zone 3", "state": "Maharashtra", "activeEngineers": 6, "totalJobs": 98}]}