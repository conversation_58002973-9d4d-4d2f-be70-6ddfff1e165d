// Dashboard JavaScript - Modern FSM Dashboard
class FSMDashboard {
    constructor() {
        this.charts = {};
        this.currentBehaviorType = 'professional';
        this.init();
    }

    init() {
        this.loadData();
        this.setupEventListeners();
        this.initializeCharts();
        this.populateTable();
    }

    loadData() {
        // Load data from JSON file
        fetch('Dashboard.json')
            .then(response => response.json())
            .then(data => {
                this.data = data;
                this.updateDashboard();
            })
            .catch(error => {
                console.log('Using sample data');
                this.data = this.getSampleData();
                this.updateDashboard();
            });
    }

    setupEventListeners() {
        // Radio button listeners for behavior type
        document.querySelectorAll('input[name="behaviorType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentBehaviorType = e.target.id;
                this.updateZoneMachineSummary();
            });
        });

        // Tab listeners for analytics
        document.querySelectorAll('#analyticsTab button').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target').substring(1);
                this.updateAnalyticsChart(targetId);
            });
        });

        // Generate button listeners
        document.querySelectorAll('button').forEach(btn => {
            if (btn.textContent.includes('Generate')) {
                btn.addEventListener('click', () => {
                    this.generateReport();
                });
            }
        });
    }

    initializeCharts() {
        // Initialize all charts
        this.initFeedbackChart();
        this.initResponseChart();
        this.initAnalyticsCharts();
    }

    initFeedbackChart() {
        const ctx = document.getElementById('feedbackChart').getContext('2d');
        this.charts.feedback = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Overall Feedback Rating', 'Zone Wise Feedback Rating', 'Parameter Wise Feedback Rating'],
                datasets: [{
                    label: 'Rating',
                    data: [3.8, 4.2, 3.5],
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5
                    }
                }
            }
        });
    }

    initResponseChart() {
        const ctx = document.getElementById('responseChart').getContext('2d');
        this.charts.response = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Feedback Not Required', 'Wrong Contact Person', 'No Feedback', 'Feedback Received'],
                datasets: [{
                    data: [30, 15, 25, 30],
                    backgroundColor: ['#ffc107', '#dc3545', '#6c757d', '#28a745'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    }

    initAnalyticsCharts() {
        // SR Chart (Pie)
        const srCtx = document.getElementById('srChart').getContext('2d');
        this.charts.sr = new Chart(srCtx, {
            type: 'pie',
            data: {
                labels: ['MC1562', 'Non-Roller', 'SP-84', 'Super 1800', 'HG 2420', 'SW 16 MC', 'HG RW WV', 'Others'],
                datasets: [{
                    data: [13, 23, 31, 44, 56, 75, 18, 144],
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', '#dc3545',
                        '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: { size: 10 }
                        }
                    }
                }
            }
        });

        // Service Request Chart (Funnel-like)
        const quotationCtx = document.getElementById('quotationChart').getContext('2d');
        this.charts.quotation = new Chart(quotationCtx, {
            type: 'bar',
            data: {
                labels: ['Daily Maintenance', 'Good Will', 'Customer Complaint', 'Normal/Payable', 'HARC', 'FOC'],
                datasets: [{
                    data: [48, 25, 88, 5, 2, 2],
                    backgroundColor: ['#17a2b8', '#ffc107', '#28a745', '#dc3545', '#e83e8c', '#6c757d']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: { display: false }
                }
            }
        });

        // Initialize other charts with similar patterns
        this.initOtherAnalyticsCharts();
    }

    initOtherAnalyticsCharts() {
        // Job Card Chart
        const jobcardCtx = document.getElementById('jobcardChart').getContext('2d');
        this.charts.jobcard = new Chart(jobcardCtx, {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'In Progress', 'Pending'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { boxWidth: 12, font: { size: 10 } }
                    }
                }
            }
        });

        // Activity Chart
        const activityCtx = document.getElementById('activityChart').getContext('2d');
        this.charts.activity = new Chart(activityCtx, {
            type: 'bar',
            data: {
                labels: ['More Than 90 Hours', '72 To 90 Hours', '48 To 72 Hours', '24 To 48 Hours', '24 To 36 Hours', '16 To 24 Hours', '8 To 16 Hours', 'Less Than 8 Hours'],
                datasets: [{
                    data: [13, 5, 5, 0, 0, 0, 0, 30],
                    backgroundColor: '#17a2b8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: { legend: { display: false } }
            }
        });

        // Machine Status Chart
        const machineCtx = document.getElementById('machineChart').getContext('2d');
        this.charts.machine = new Chart(machineCtx, {
            type: 'bar',
            data: {
                labels: ['Running', 'Maintenance', 'Breakdown', 'Idle'],
                datasets: [{
                    data: [45, 20, 15, 20],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // Escalation Chart
        const escalationCtx = document.getElementById('escalationChart').getContext('2d');
        this.charts.escalation = new Chart(escalationCtx, {
            type: 'line',
            data: {
                labels: ['Level 1', 'Level 2', 'Level 3', 'Level 4'],
                datasets: [{
                    data: [25, 15, 8, 2],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // Employee Workload Chart
        const workloadCtx = document.getElementById('workloadChart').getContext('2d');
        this.charts.workload = new Chart(workloadCtx, {
            type: 'bar',
            data: {
                labels: ['Engineer 1', 'Engineer 2', 'Engineer 3', 'Engineer 4', 'Engineer 5'],
                datasets: [{
                    label: 'Active Jobs',
                    data: [12, 8, 15, 6, 10],
                    backgroundColor: '#007bff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });
    }

    updateZoneMachineSummary() {
        // Update table based on selected behavior type
        const tableData = this.getTableDataForBehaviorType(this.currentBehaviorType);
        this.populateTable(tableData);
    }

    getTableDataForBehaviorType(behaviorType) {
        // Return different data based on behavior type
        const baseData = this.getSampleTableData();

        switch(behaviorType) {
            case 'professional':
                return baseData.filter(row => row.category === 'professional');
            case 'employee':
                return baseData.filter(row => row.category === 'employee');
            case 'parts':
                return baseData.filter(row => row.category === 'parts');
            case 'overall':
                return baseData;
            default:
                return baseData;
        }
    }

    populateTable(data = null) {
        const tableBody = document.getElementById('tableBody');
        const tableData = data || this.getSampleTableData();

        tableBody.innerHTML = '';

        tableData.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${row.branch}</td>
                <td>${row.zone}</td>
                <td>${row.state}</td>
                <td>${row.serviceEngineer}</td>
                <td>${row.transactionDate}</td>
                <td>${row.partyName}</td>
                <td>${row.brand}</td>
                <td>${row.model}</td>
                <td>${row.serial}</td>
                <td>${row.jobAddress}</td>
                <td><span class="badge bg-${row.statusColor}">${row.transactionStatus}</span></td>
                <td>${row.ageing}</td>
                <td>${row.sro}</td>
                <td>${row.quotation}</td>
                <td>${row.srjc}</td>
                <td><span class="badge bg-${row.jcStatusColor}">${row.jcStatus}</span></td>
            `;
            tableBody.appendChild(tr);
        });
    }

    updateAnalyticsChart(chartType) {
        // Update specific analytics chart when tab is selected
        if (this.charts[chartType]) {
            this.charts[chartType].update();
        }
    }

    generateReport() {
        // Show loading state
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            if (btn.textContent.includes('Generate')) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
                btn.disabled = true;
            }
        });

        // Simulate report generation
        setTimeout(() => {
            this.updateDashboard();
            buttons.forEach(btn => {
                if (btn.disabled) {
                    btn.innerHTML = btn.innerHTML.includes('chart-line') ?
                        '<i class="fas fa-chart-line me-1"></i>Generate' :
                        '<i class="fas fa-sync me-1"></i>Generate';
                    btn.disabled = false;
                }
            });
        }, 1500);
    }

    updateDashboard() {
        // Update all dashboard components
        this.updateZoneMachineSummary();
        this.updateCharts();
        this.updateZoneMap();
    }

    updateCharts() {
        // Update all charts with new data
        Object.keys(this.charts).forEach(chartKey => {
            if (this.charts[chartKey]) {
                this.charts[chartKey].update();
            }
        });
    }

    updateZoneMap() {
        // Update zone map statistics
        const stats = this.calculateZoneStats();
        const badges = document.querySelectorAll('.badge');
        if (badges.length >= 3) {
            badges[badges.length - 3].textContent = `Started: ${stats.started}`;
            badges[badges.length - 2].textContent = `Pending: ${stats.pending}`;
            badges[badges.length - 1].textContent = `Complete: ${stats.complete}`;
        }
    }

    calculateZoneStats() {
        // Calculate zone statistics
        return {
            started: Math.floor(Math.random() * 20) + 5,
            pending: Math.floor(Math.random() * 10) + 2,
            complete: Math.floor(Math.random() * 25) + 10
        };
    }

    getSampleTableData() {
        return [
            {
                branch: 'Mumbai',
                zone: 'Zone 1',
                state: 'Maharashtra',
                serviceEngineer: 'John Doe',
                transactionDate: '2025-08-15',
                partyName: 'ABC Industries',
                brand: 'Brand A',
                model: 'Model X1',
                serial: 'SN001',
                jobAddress: 'Mumbai, MH',
                transactionStatus: 'Completed',
                statusColor: 'success',
                ageing: '5 days',
                sro: 'SRO001',
                quotation: 'Q001',
                srjc: 'SR001',
                jcStatus: 'Closed',
                jcStatusColor: 'success',
                category: 'professional'
            },
            {
                branch: 'Pune',
                zone: 'Zone 2',
                state: 'Maharashtra',
                serviceEngineer: 'Jane Smith',
                transactionDate: '2025-08-20',
                partyName: 'XYZ Corp',
                brand: 'Brand B',
                model: 'Model Y2',
                serial: 'SN002',
                jobAddress: 'Pune, MH',
                transactionStatus: 'In Progress',
                statusColor: 'warning',
                ageing: '2 days',
                sro: 'SRO002',
                quotation: 'Q002',
                srjc: 'SR002',
                jcStatus: 'Open',
                jcStatusColor: 'primary',
                category: 'employee'
            },
            {
                branch: 'Nagpur',
                zone: 'Zone 3',
                state: 'Maharashtra',
                serviceEngineer: 'Mike Johnson',
                transactionDate: '2025-08-25',
                partyName: 'DEF Ltd',
                brand: 'Brand C',
                model: 'Model Z3',
                serial: 'SN003',
                jobAddress: 'Nagpur, MH',
                transactionStatus: 'Pending',
                statusColor: 'danger',
                ageing: '1 day',
                sro: 'SRO003',
                quotation: 'Q003',
                srjc: 'SR003',
                jcStatus: 'Pending',
                jcStatusColor: 'warning',
                category: 'parts'
            }
        ];
    }

    getSampleData() {
        return {
            zones: ['Zone 1', 'Zone 2', 'Zone 3-6'],
            equipment: ['Equipment 1', 'Equipment 2'],
            branches: ['Pune', 'Mumbai', 'Nagpur'],
            behaviorTypes: {
                professional: { rating: 4.2, feedback: 85 },
                employee: { rating: 3.8, feedback: 78 },
                parts: { rating: 4.0, feedback: 82 },
                overall: { rating: 4.0, feedback: 81 }
            }
        };
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new FSMDashboard();
});